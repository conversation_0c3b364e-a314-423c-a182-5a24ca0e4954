import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create categories
  console.log('Creating categories...')
  const toolCategories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Power Tools',
        type: 'TOOL',
        description: 'Electric and battery-powered tools'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Hand Tools',
        type: 'TOOL',
        description: 'Manual tools and equipment'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Measuring Tools',
        type: 'TOOL',
        description: 'Measurement and precision tools'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Safety Equipment',
        type: 'TOOL',
        description: 'Personal protective equipment'
      }
    })
  ])

  const materialCategories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Construction Materials',
        type: 'MATERIAL',
        description: 'Building and construction materials'
      }
    }),
    prisma.category.create({
      data: {
        name: 'Electrical Materials',
        type: 'MATERIAL',
        description: 'Electrical components and wiring'
      }
    })
  ])

  // Create users
  console.log('Creating users...')
  const users = await Promise.all([
    prisma.user.create({
      data: {
        name: '<PERSON>i <PERSON>o',
        email: '<EMAIL>',
        phone: '+62812345678',
        department: 'Construction'
      }
    }),
    prisma.user.create({
      data: {
        name: 'Sari Dewi',
        email: '<EMAIL>',
        phone: '+62812345679',
        department: 'Engineering'
      }
    }),
    prisma.user.create({
      data: {
        name: 'Ahmad Wijaya',
        email: '<EMAIL>',
        phone: '+62812345680',
        department: 'Maintenance'
      }
    }),
    prisma.user.create({
      data: {
        name: 'Rina Kusuma',
        email: '<EMAIL>',
        phone: '+62812345681',
        department: 'Project Management'
      }
    }),
    prisma.user.create({
      data: {
        name: 'Dedi Kurniawan',
        email: '<EMAIL>',
        phone: '+62812345682',
        department: 'Maintenance'
      }
    })
  ])

  // Create tools
  console.log('Creating tools...')
  const tools = await Promise.all([
    prisma.tool.create({
      data: {
        name: 'Kunci Angin',
        categoryId: toolCategories[0].id, // Power Tools
        condition: 'GOOD',
        totalQuantity: 10,
        availableQuantity: 8,
        location: 'Warehouse A',
        supplier: 'Tool Corp',
        purchasePrice: 1500000
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Set Bor',
        categoryId: toolCategories[0].id, // Power Tools
        condition: 'EXCELLENT',
        totalQuantity: 15,
        availableQuantity: 15,
        location: 'Warehouse A',
        supplier: 'Tool Corp',
        purchasePrice: 2500000
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Meteran',
        categoryId: toolCategories[2].id, // Measuring Tools
        condition: 'GOOD',
        totalQuantity: 25,
        availableQuantity: 22,
        location: 'Warehouse B',
        supplier: 'Precision Tools Ltd',
        purchasePrice: 150000
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Perlengkapan Keselamatan',
        categoryId: toolCategories[3].id, // Safety Equipment
        condition: 'GOOD',
        totalQuantity: 10,
        availableQuantity: 7,
        location: 'Safety Storage',
        supplier: 'Safety First Inc',
        purchasePrice: 500000
      }
    }),
    prisma.tool.create({
      data: {
        name: 'Gergaji Listrik',
        categoryId: toolCategories[0].id, // Power Tools
        condition: 'GOOD',
        totalQuantity: 5,
        availableQuantity: 4,
        location: 'Warehouse A',
        supplier: 'Tool Corp',
        purchasePrice: 3500000
      }
    })
  ])

  // Create materials
  console.log('Creating materials...')
  const materials = await Promise.all([
    prisma.material.create({
      data: {
        name: 'Besi Beton',
        categoryId: materialCategories[0].id, // Construction Materials
        currentQuantity: 150,
        thresholdQuantity: 50,
        unit: 'kg',
        location: 'Storage Section B-12',
        supplier: 'Materials Ltd',
        unitPrice: 15000
      }
    }),
    prisma.material.create({
      data: {
        name: 'Semen Cor',
        categoryId: materialCategories[0].id, // Construction Materials
        currentQuantity: 500,
        thresholdQuantity: 100,
        unit: 'kg',
        location: 'Storage Section A-5',
        supplier: 'Materials Ltd',
        unitPrice: 12000
      }
    }),
    prisma.material.create({
      data: {
        name: 'Kabel Listrik',
        categoryId: materialCategories[1].id, // Electrical Materials
        currentQuantity: 1000,
        thresholdQuantity: 200,
        unit: 'meter',
        location: 'Electrical Storage',
        supplier: 'Electric Supply Co',
        unitPrice: 25000
      }
    }),
    prisma.material.create({
      data: {
        name: 'Kawat Las',
        categoryId: materialCategories[1].id, // Electrical Materials
        currentQuantity: 25,
        thresholdQuantity: 50,
        unit: 'kg',
        location: 'Welding Section',
        supplier: 'Welding Supplies Inc',
        unitPrice: 45000
      }
    })
  ])

  // Create some borrowing transactions
  console.log('Creating borrowing transactions...')
  const borrowing1 = await prisma.borrowingTransaction.create({
    data: {
      userId: users[0].id, // Budi Santoso
      dueDate: new Date('2024-01-20'),
      purpose: 'Proyek Konstruksi Gedung A',
      status: 'ACTIVE'
    }
  })

  await prisma.borrowingItem.create({
    data: {
      borrowingTransactionId: borrowing1.id,
      toolId: tools[0].id, // Kunci Angin
      quantity: 2,
      originalCondition: 'GOOD'
    }
  })

  const borrowing2 = await prisma.borrowingTransaction.create({
    data: {
      userId: users[1].id, // Sari Dewi
      dueDate: new Date('2024-01-12'),
      purpose: 'Survei Lokasi Proyek',
      status: 'OVERDUE'
    }
  })

  await prisma.borrowingItem.create({
    data: {
      borrowingTransactionId: borrowing2.id,
      toolId: tools[2].id, // Meteran
      quantity: 3,
      originalCondition: 'GOOD'
    }
  })

  // Create some consumption transactions
  console.log('Creating consumption transactions...')
  const consumption1 = await prisma.consumptionTransaction.create({
    data: {
      userId: users[3].id, // Rina Kusuma
      purpose: 'Pekerjaan Pondasi',
      projectName: 'Project A',
      totalValue: 1950000
    }
  })

  await prisma.consumptionItem.create({
    data: {
      consumptionTransactionId: consumption1.id,
      materialId: materials[0].id, // Besi Beton
      quantity: 50,
      unitPrice: 15000,
      totalValue: 750000
    }
  })

  await prisma.consumptionItem.create({
    data: {
      consumptionTransactionId: consumption1.id,
      materialId: materials[1].id, // Semen Cor
      quantity: 100,
      unitPrice: 12000,
      totalValue: 1200000
    }
  })

  // Create another consumption
  const consumption2 = await prisma.consumptionTransaction.create({
    data: {
      userId: users[4].id, // Dedi Kurniawan
      purpose: 'Pemeliharaan Instalasi Listrik',
      projectName: 'Maintenance Q1',
      totalValue: 1125000
    }
  })

  await prisma.consumptionItem.create({
    data: {
      consumptionTransactionId: consumption2.id,
      materialId: materials[2].id, // Kabel Listrik
      quantity: 45,
      unitPrice: 25000,
      totalValue: 1125000
    }
  })

  // Update material quantities after consumption
  await prisma.material.update({
    where: { id: materials[0].id },
    data: { currentQuantity: 100 } // 150 - 50
  })

  await prisma.material.update({
    where: { id: materials[1].id },
    data: { currentQuantity: 400 } // 500 - 100
  })

  await prisma.material.update({
    where: { id: materials[2].id },
    data: { currentQuantity: 955 } // 1000 - 45
  })

  // Create some completed borrowings for history
  const completedBorrowing = await prisma.borrowingTransaction.create({
    data: {
      userId: users[2].id, // Ahmad Wijaya
      dueDate: new Date('2024-01-10'),
      returnDate: new Date('2024-01-09'),
      purpose: 'Pemeliharaan Rutin',
      status: 'COMPLETED'
    }
  })

  await prisma.borrowingItem.create({
    data: {
      borrowingTransactionId: completedBorrowing.id,
      toolId: tools[1].id, // Set Bor
      quantity: 1,
      originalCondition: 'EXCELLENT',
      returnCondition: 'GOOD',
      returnDate: new Date('2024-01-09')
    }
  })

  console.log('✅ Database seeding completed successfully!')
  console.log(`Created:
  - ${toolCategories.length + materialCategories.length} categories
  - ${users.length} users
  - ${tools.length} tools
  - ${materials.length} materials
  - 3 borrowing transactions with items (2 active, 1 completed)
  - 2 consumption transactions with items`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
