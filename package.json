{"name": "toolflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.22.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "mysql2": "^3.11.3", "next": "15.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.6.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "15.1.3", "postcss": "^8.5.2", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "overrides": {"inflight": "npm:@isaacs/inflight@^1.0.6"}}