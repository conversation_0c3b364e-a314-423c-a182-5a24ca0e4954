import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UpdateToolSchema } from '@/lib/validations'
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  handleDatabaseError,
  logActivity
} from '@/lib/api-utils'

// GET /api/tools/[id] - Get tool by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tool = await prisma.tool.findUnique({
      where: { id: params.id },
      include: {
        category: {
          select: { name: true, type: true }
        },
        borrowingItems: {
          where: {
            borrowingTransaction: {
              status: { in: ['ACTIVE', 'OVERDUE'] }
            }
          },
          include: {
            borrowingTransaction: {
              include: {
                user: {
                  select: { name: true, email: true }
                }
              }
            }
          }
        },
        _count: {
          select: {
            borrowingItems: {
              where: {
                borrowingTransaction: {
                  status: { in: ['ACTIVE', 'OVERDUE'] }
                }
              }
            }
          }
        }
      },
    })

    if (!tool) {
      return errorResponse('Tool not found', 404)
    }

    return successResponse({
      ...tool,
      hasActiveBorrowing: tool._count.borrowingItems > 0,
      borrowedQuantity: tool.totalQuantity - tool.availableQuantity,
    })
  } catch (error) {
    console.error('Error fetching tool:', error)
    return handleDatabaseError(error)
  }
}

// PUT /api/tools/[id] - Update tool
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const validation = await validateRequest(request, UpdateToolSchema)
    if (!validation.success) {
      return validation.response
    }

    // Get existing tool
    const existingTool = await prisma.tool.findUnique({
      where: { id: params.id },
    })

    if (!existingTool) {
      return errorResponse('Tool not found', 404)
    }

    const data = validation.data

    // Verify category exists and is of type TOOL if categoryId is being updated
    if (data.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: data.categoryId },
      })

      if (!category) {
        return errorResponse('Category not found', 404)
      }

      if (category.type !== 'TOOL') {
        return errorResponse('Category must be of type TOOL', 400)
      }
    }

    // Business logic validation
    if (data.totalQuantity !== undefined || data.availableQuantity !== undefined) {
      const newTotalQuantity = data.totalQuantity ?? existingTool.totalQuantity
      const newAvailableQuantity = data.availableQuantity ?? existingTool.availableQuantity

      // Check if available quantity doesn't exceed total quantity
      if (newAvailableQuantity > newTotalQuantity) {
        return errorResponse('Available quantity cannot exceed total quantity', 400)
      }

      // Check if reducing total quantity below borrowed amount
      const borrowedQuantity = existingTool.totalQuantity - existingTool.availableQuantity
      if (newTotalQuantity < borrowedQuantity) {
        return errorResponse(
          `Cannot reduce total quantity below borrowed amount (${borrowedQuantity} currently borrowed)`,
          400
        )
      }

      // Adjust available quantity if total quantity is reduced
      if (data.totalQuantity !== undefined && data.availableQuantity === undefined) {
        data.availableQuantity = newTotalQuantity - borrowedQuantity
      }
    }

    // Update tool
    const updatedTool = await prisma.tool.update({
      where: { id: params.id },
      data: {
        ...data,
        purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : undefined,
      },
      include: {
        category: {
          select: { name: true, type: true }
        },
        _count: {
          select: {
            borrowingItems: {
              where: {
                borrowingTransaction: {
                  status: { in: ['ACTIVE', 'OVERDUE'] }
                }
              }
            }
          }
        }
      },
    })

    // Log activity
    await logActivity('TOOL', params.id, 'UPDATE', undefined, existingTool, updatedTool)

    return successResponse({
      ...updatedTool,
      hasActiveBorrowing: updatedTool._count.borrowingItems > 0,
      borrowedQuantity: updatedTool.totalQuantity - updatedTool.availableQuantity,
    }, 'Tool updated successfully')
  } catch (error) {
    console.error('Error updating tool:', error)
    return handleDatabaseError(error)
  }
}

// DELETE /api/tools/[id] - Delete tool
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if tool exists
    const tool = await prisma.tool.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            borrowingItems: {
              where: {
                borrowingTransaction: {
                  status: { in: ['ACTIVE', 'OVERDUE'] }
                }
              }
            }
          }
        }
      },
    })

    if (!tool) {
      return errorResponse('Tool not found', 404)
    }

    // Check if tool has active borrowings
    if (tool._count.borrowingItems > 0) {
      return errorResponse(
        'Cannot delete tool. It has active borrowings.',
        400
      )
    }

    // Delete tool
    await prisma.tool.delete({
      where: { id: params.id },
    })

    // Log activity
    await logActivity('TOOL', params.id, 'DELETE', undefined, tool, undefined)

    return successResponse(null, 'Tool deleted successfully')
  } catch (error) {
    console.error('Error deleting tool:', error)
    return handleDatabaseError(error)
  }
}
